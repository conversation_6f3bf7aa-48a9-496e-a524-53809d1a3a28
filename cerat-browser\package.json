{"name": "cerat-browser", "version": "1.0.0", "description": "A lightweight, privacy-focused web browser with built-in ad/tracker blocking and incognito mode by default", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "dev": "electron . --dev"}, "keywords": ["browser", "privacy", "electron", "adblock", "tracker-blocking", "incognito"], "author": "<PERSON><PERSON>rowser Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-store": "^8.1.0"}, "build": {"appId": "com.cerat.browser", "productName": "<PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}